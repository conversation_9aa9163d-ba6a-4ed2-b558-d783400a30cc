/*eslint-disable*/
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import html2canvas from 'html2canvas';
import { DataReceivedEvent } from '../../../../utils/constants';
import { ReactComponent as CaptureIcon } from '../../../../assets/icons/Copy.svg';
import { SaasService } from '../../../services/saasServices';
import { useSaasHelpers } from '../../../helpers/helpers';
import './ScreenCaptureButton.scss';

export function ScreenCaptureButton({
  room,
  screenShareTracks,
  // focusTrack,
  setToastNotification,
  setToastStatus,
  setShowToast,
  setShowPopover,
  meetingDetails
}) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [shouldDownload, setShouldDownload] = useState(true); // State to control download
  const { saasHostToken } = useSaasHelpers();

  // Automatically determine capture mode based on screen sharing status
  const captureMode = (screenShareTracks && screenShareTracks.length > 0) ? 'screenshare' : 'gridlayout';

  const uploadScreenshot = async (formData) => {
    try {
      const response = await SaasService.uploadScreenshot(
        formData,
        { "Content-Type": "multipart/form-data" },
        saasHostToken
      );
      if (response.success === 0) {
        throw new Error("Failed to upload screenshot");
      }
      return response;
    } catch (error) {
      console.error("Screenshot upload error:", error);
      throw new Error(`Screenshot upload failed: ${error.message}`);
    }
  };



  const captureFocusedScreenShare = async () => {
    try {
      const screenShareVideo = document.querySelector('video[data-lk-source="screen_share"]');

      if (!screenShareVideo) {
        throw new Error("Screen share video not found - make sure screen sharing is active");
      }

      if (screenShareVideo.videoWidth === 0 || screenShareVideo.videoHeight === 0) {
        throw new Error("Screen share video has no dimensions - video may not be loaded yet");
      }



      // Create canvas to capture the screen share video AS IT APPEARS (with blur if blurred)
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = screenShareVideo.videoWidth;
      canvas.height = screenShareVideo.videoHeight;

      // Draw the current video frame to canvas exactly as it appears
      ctx.drawImage(screenShareVideo, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob and process
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error("Failed to create blob from screen share video canvas"));
            return;
          }

          const formData = new FormData();
          const eventName = meetingDetails?.event_name?.replace(/[^a-zA-Z0-9]/g, '-') || 'screenshot';
          const filename = `${eventName}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
          const file = new File([blob], filename, { type: 'image/png' });
          formData.append('image', file);
          if (room?.roomInfo?.name) {
            formData.append('meeting_uid', room.roomInfo.name);
          }

          // Upload to API
          uploadScreenshot(formData);

          // Download locally only if shouldDownload is true
          if (shouldDownload) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }

          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("Focused screen share capture error:", error);
      throw new Error(`Focused capture failed: ${error.message}`);
    }
  };

  const captureVideoConference = async () => {
    try {
      // Try to capture the entire video conference view first
      let targetElement = document.querySelector('.lk-video-conference');

      // If not found, fallback to the inner container
      if (!targetElement) {
        targetElement = document.querySelector('.lk-video-conference-inner');
      }

      // If still not found, fallback to just the grid layout
      if (!targetElement) {
        targetElement = document.querySelector('.lk-grid-layout-wrapper');
      }

      if (!targetElement) {
        throw new Error("Video conference view not found - make sure you're in the video conference");
      }

      // Calculate height excluding control bar space
      const controlBar = document.querySelector('.lk-control-bar, .control-bar-container');
      const controlBarHeight = controlBar ? controlBar.offsetHeight : 0;
      const captureHeight = targetElement.offsetHeight - controlBarHeight;

      // Use html2canvas to capture the video conference view excluding control bar
      const canvas = await html2canvas(targetElement, {
        useCORS: true,
        allowTaint: true,
        scale: 1,
        backgroundColor: '#000000',
        logging: false,
        width: targetElement.offsetWidth,
        height: captureHeight,
        y: 0, // Start from top
        x: 0, // Start from left
        // Handle CORS issues with external images
        proxy: undefined,
        foreignObjectRendering: false,
        onclone: (clonedDoc) => {
          // Replace external images with placeholder or remove them to avoid CORS issues
          const externalImages = clonedDoc.querySelectorAll('img[src*="daakia-media.daakia.co.in"], img[src*="http"], img[src*="https"]');
          externalImages.forEach(img => {
            // Skip if it's from the same origin
            if (img.src.startsWith(window.location.origin)) return;

            // For external images, try to handle CORS or hide them
            img.style.display = 'none';
            // Alternative: replace with a placeholder
            // img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+TG9nbzwvdGV4dD48L3N2Zz4=';
          });
        },
        ignoreElements: (element) => {
          // Exclude control bar and logo from screenshot
          return element.classList.contains('lk-control-bar') ||
                 element.classList.contains('control-bar-container') ||
                 element.classList.contains('daakia-logo') ||
                 element.classList.contains('saas-branding') ||
                 element.closest('.lk-control-bar') ||
                 element.closest('.control-bar-container') ||
                 element.closest('.daakia-logo') ||
                 element.closest('.saas-branding');
        }
      });



      // Convert canvas to blob and process
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error("Failed to create blob from video conference canvas"));
            return;
          }

          const formData = new FormData();
          const eventName = meetingDetails?.event_name?.replace(/[^a-zA-Z0-9]/g, '-') || 'screenshot';
          const filename = `${eventName}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
          const file = new File([blob], filename, { type: 'image/png' });
          formData.append('image', file);
          if (room?.roomInfo?.name) {
            formData.append('meeting_uid', room.roomInfo.name);
          }
          // Upload to API
          uploadScreenshot(formData);

          // Download locally only if shouldDownload is true
          if (shouldDownload) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }

          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("Video conference capture error:", error);

      // Handle CORS-related errors more gracefully
      if (error.message.includes('CORS') || error.message.includes('cross-origin') || error.message.includes('tainted')) {
        throw new Error(`Screenshot failed due to external images. This is a browser security restriction.`);
      }

      throw new Error(`Video conference capture failed: ${error.message}`);
    }
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    await room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);

    try {
      if (captureMode === 'screenshare') {
        // Check if screen sharing is active for screenshare mode
        if (!screenShareTracks || screenShareTracks.length === 0) {
          setToastNotification("No active screen share to capture");
          setToastStatus("warning");
          setShowToast(true);
          setIsCapturing(false);
          return;
        }
        // Capture the focused screen share from focus layout
        await captureFocusedScreenShare();
      } else if (captureMode === 'gridlayout') {
        // Capture the entire video conference view
        await captureVideoConference();
      }

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      setToastNotification("Screen capture successful");
      setToastStatus("success");
      setShowToast(true);

      // Close the popover
      if (setShowPopover) {
        setShowPopover(false);
      }

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  useEffect(() => {

    const handleKeyDown = (event) => {
      if (event.shiftKey && event.key.toLowerCase() === 's') {
        // Allow capture in both modes - no need to check for screen share tracks in grid mode
        if (!isCapturing) {
          event.preventDefault();
          handleScreenCapture();
        }
      }
    };

    const handleKeyUp = () => {
      // No need for key tracking with simple Shift + S
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isCapturing, captureMode]);

  return (
    <div
      onClick={handleScreenCapture}
      className={`screen-capture-button ${isCapturing ? 'capturing' : ''}`}
      title={`Capture ${captureMode === 'screenshare' ? 'Screen Share' : 'Video Conference'} (Shift+S)`}
    >
      <CaptureIcon
        style={{ color: isCapturing ? '#1890ff' : '#fff' }}
      />
    </div>
  );
}
